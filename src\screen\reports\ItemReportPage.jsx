import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import Navbar from '../../components/Navbar';

const ItemReportPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { itemData, fromDate, toDate } = route.params;

  return (
    <View style={styles.container}>
      <Navbar />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <Text style={styles.title}>Item Report</Text>
      </View>

      {/* Report Info */}
      <View style={styles.reportInfo}>
        <Text style={styles.infoText}>
          Period: {fromDate?.toLocaleDateString()} - {toDate?.toLocaleDateString()}
        </Text>
        <Text style={styles.infoText}>Total Items: {itemData?.length || 0}</Text>
      </View>

      {/* Items List */}
      <ScrollView style={styles.itemsList}>
        {itemData?.map((item, index) => (
          <View key={index} style={styles.itemItem}>
            <Text style={styles.itemText}>Item ID: {item.ItemID || 'N/A'}</Text>
            <Text style={styles.itemText}>Item Name: {item.ItemName || 'N/A'}</Text>
            <Text style={styles.itemText}>Quantity: {item.Quantity || '0'}</Text>
            <Text style={styles.itemText}>Amount: ₹{item.Amount || '0'}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6E6E6',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black',
  },
  reportInfo: {
    backgroundColor: 'white',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoText: {
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  itemsList: {
    flex: 1,
    paddingHorizontal: 10,
  },
  itemItem: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  itemText: {
    fontSize: 14,
    marginBottom: 3,
    color: '#333',
  },
});

export default ItemReportPage;
